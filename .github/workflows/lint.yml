name: Lint Code

on:
  pull_request:
    branches: [ main, master, develop ]
    types: [opened, synchronize, reopened]

jobs:
  lint:
    name: Run ESLint
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 'lts/*'
          cache: 'yarn'
          
      - name: Install dependencies
        run: yarn install --frozen-lockfile
        
      - name: Run lint
        run: yarn lint
        
      - name: Comment PR on lint failure
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: '❌ **Lint check failed!** Please fix the linting errors and push your changes.'
            })
